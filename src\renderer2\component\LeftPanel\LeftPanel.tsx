import { Box, styled } from '@mui/material';
import styles from "./LeftPanel.module.scss";
import { ReactComponent as PinIcon } from '../../assets/New-images/Side-Menu-Pin-42.svg';
import { ReactComponent as PinActiveIcon } from '../../assets/New-images/Side-Menu-Pin-42-Active.svg';
import { ReactComponent as HomeIcon } from '../../assets/New-images/Side-Menu-Home-42.svg';
import { ReactComponent as HomeActiveIcon } from '../../assets/New-images/Side-Menu-Home-42-Active.svg';
import { ReactComponent as NotificationIcon } from '../../assets/New-images/Side-Menu-Notification-42.svg';
import { ReactComponent as NotificationActiveIcon } from '../../assets/New-images/Side-Menu-Notification-42-Active.svg';
import { ReactComponent as QuestionIcon } from '../../assets/New-images/Side-Menu-Question-42.svg';
import { ReactComponent as QuestionActiveIcon } from '../../assets/New-images/Side-Menu-Question-42-Active.svg';
import { ReactComponent as ChatIcon } from '../../assets/New-images/Side-Menu-Chat-42.svg';
import { ReactComponent as ChatActiveIcon } from '../../assets/New-images/Side-Menu-Chat-42-Active.svg';
import { ReactComponent as VideoIcon } from '../../assets/New-images/Side-Menu-Video-42.svg';
import { ReactComponent as VideoActiveIcon } from '../../assets/New-images/Side-Menu-Video-42-Active.svg';
import { ReactComponent as PinIcons } from '../../assets/New-images/Side-Menu-Pin-36.svg';
import { ReactComponent as PinActiveIcons } from '../../assets/New-images/Side-Menu-Pin-36-Active.svg';
import { ReactComponent as HomeIcons } from '../../assets/New-images/Side-Menu-Home-36.svg';
import { ReactComponent as HomeActiveIcons } from '../../assets/New-images/Side-Menu-Home-36-Active.svg';
import { ReactComponent as NotificationIcons } from '../../assets/New-images/Side-Menu-Notification-36.svg';
import { ReactComponent as NotificationActiveIcons } from '../../assets/New-images/Side-Menu-Notification-36-Active.svg';
import { ReactComponent as QuestionIcons } from '../../assets/New-images/Side-Menu-Question-36.svg';
import { ReactComponent as QuestionActiveIcons } from '../../assets/New-images/Side-Menu-Question-36-Active.svg';
import { ReactComponent as ChatIcons } from '../../assets/New-images/Side-Menu-Chat-36.svg';
import { ReactComponent as ChatActiveIcons } from '../../assets/New-images/Side-Menu-Chat-36-Active.svg';
import { ReactComponent as VideoIcons } from '../../assets/New-images/Side-Menu-Video-36.svg';
import { ReactComponent as VideoActiveIcons } from '../../assets/New-images/Side-Menu-Video-36-Active.svg';
import { ReactComponent as SearchIcon } from '../../assets/New-images/Search.svg';
import clsx from "clsx";
import { useLeftPanelStore } from "./LeftPanelStore";
import { routes, navigationConfirmMessages } from "src/renderer2/common";
import { getChannelWindow, useAuthStore, localStorageStickyItemKey, useCreatePoStore, getSocketConnection, useGlobalStore } from "@bryzos/giss-ui-library";
import { navigatePage } from "src/renderer2/helper";
import { useEffect, useState, useMemo } from 'react';
import useDialogStore from '../DialogPopup/DialogStore';
import SavedBomLeftPanel from './SavedBomLeftPanel/SavedBomLeftPanel';
import useGetBomSavedData from 'src/renderer2/hooks/useGetBOMSavedData';
import SharedPricingHistoryWindow from '../SharedPricingHistoryWindow/SharedPricingHistoryWindow';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import SearchHistoryWindow from '../SearchHistoryWindow/SearchHistoryWindow';
import { useHoverVideoStore } from './HoverVideoStore';
import { useBomPdfExtractorStore } from 'src/renderer2/pages/buyer/BomPdfExtractor/BomPdfExtractorStore';
import ViewPoHistoryLeftPanel from './ViewPoHistoryLeftPanel/ViewPoHistoryLeftPanel';
import MyChats from '../ChatWithVendor/MyChats';

// Create a styled component for the main content
const Main = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open?: boolean }>(({ theme, open }) => ({
  flexGrow: 1,
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: 0,
  ...(open && {
    marginLeft: '240px',
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));

interface MenuProps {
  routerContentRef: React.RefObject<HTMLDivElement>;
  isMenuOpen: boolean;
  updateBackdropOverlay: boolean;
}

const menuItems = [
  {
    section: 'ADMIN',
    items: [
      { label: 'Impersonate User', route: routes.impersonateList },
      { label: 'Exit Impersonation', route: '' }
    ]
  },
  {
    section: 'ESTIMATING',
    items: [
      { label: 'Price Search', route: routes.homePage },
      { label: 'Saved Searches', route: '' },
      { label: 'Shared Pricing', route: '' }
    ]
  },
  {
    section: 'PURCHASING',
    items: [
      { label: 'Create New PO (Blank)', route: routes.createPoPage },
      { label: 'Saved BOM', route: routes.savedBom, videoHoverId:'saved-bom' },
      { label: 'Upload New BOM', route: [routes.bomUploadReview, routes.bomUpload], videoHoverId:'upload-bom' }
    ]
  },
  {
    section: 'ORDER MANAGEMENT',
    items: [
      { label: 'View PO History', route: routes.viewPoHistory },
      // { label: 'Edit an Active Order', route: '' },
      // { label: 'Create a Material Dispute', route: '' },
      // { label: 'Request an Invoice Adjustment'}
    ]
  },
  {
    section: 'MY ACCOUNT',
    items: [
      { label: 'Profile Settings', route: routes.buyerSettingPage },
      { label: 'Subscription - Billing', route: routes.subscribe }
    ]
  }
];

const menuItemsSeller = [
  {
    section: 'ADMIN',
    items: [
      { label: 'Impersonate User', route: routes.impersonateList },
      { label: 'Exit Impersonation', route: '' }
    ]
  },
  {
    section: 'SELLING',
    items: [
      { label: 'View Available Orders Summary', route: routes.orderPage },
      // { label: 'View Specific Order Details', route: '' },
      // { label: 'Set Claim Preferences', route: '' }
    ]
  },
  // {
  //   section: 'ORDER MANAGEMENT',
  //   items: [
  //     { label: 'View Sales History', route: '' },
  //     { label: 'Edit an Active Order', route: '' },
  //     { label: 'Upload Packing Lists & MTRs', route: '' },
  //     { label: 'Issue a Credit Memo', route: '' }
  //   ]
  // },
  {
    section:"My Chats",
    items: [
      { label: 'Chats', route: '' }
    ]
  },
  {
    section: 'MY ACCOUNT',
    items: [
      { label: 'Profile Settings', route: routes.sellerSettingPage },
      // { label: '(blank)', route: '' }
    ]
  }
];

const LeftPanel: React.FC<MenuProps> = ({ routerContentRef, isMenuOpen, updateBackdropOverlay }) => {
  const { userData , isImpersonatedUserLoggedIn, setTriggerExitImpersonation, userSubscription, noInternetAccessibility, apiFailureDueToNoInternet, onlineStatus} = useGlobalStore();
  const { openLeftPanel, setCloseWithoutAnimation, setOpenLeftPanel, setDisplayLeftPanel, displayLeftPanel } = useLeftPanelStore();
  const {initiateLogout} = useAuthStore();
  const { isCreatePoDirty, isCreatePOModule, setIsCreatePoDirty , bomDataIdToRefresh, setBomDataIdToRefresh} = useCreatePoStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const channelWindow = getChannelWindow();
  const [searchQuery, setSearchQuery] = useState(''); // Add this state
  const [isSticky, setIsSticky] = useState(false);
  const { leftPanelData, viewPoHistoryData } = useLeftPanelStore();
  const [leftPanelHeight, setLeftPanelHeight] = useState(0);
  const {bomProgressSocketData} = useCreatePoStore();
  const { setLoadComponent, setIsSharedPricingHistory, isSharedPricingHistory, setIsPriceSearchHistory, isPriceSearchHistory } = useRightWindowStore();
  const { isHoverVideoEnabled, toggleHoverVideo } = useHoverVideoStore();
  const { setShowBackToBomUploadButton } = useBomPdfExtractorStore();

  const showLeftPanel = openLeftPanel
  const isAdmin = !!userData?.data?.is_super_admin;
  const currentTandC = userData?.data?.current_tnc_version;
  const acceptedTandC = userData?.data?.accepted_terms_and_condition;

  const filteredMenuItems = menuItems
    .filter(section => (isAdmin || isImpersonatedUserLoggedIn) || section.section !== 'ADMIN')
    .map(section => ({
      ...section,
      items: section.items.filter(item =>
        item.label.toLowerCase().includes(searchQuery.toLowerCase())
      )
    })).filter(section => section.items.length > 0);
  
  const filteredMenuItemsSeller = menuItemsSeller
    .filter(section => (isAdmin || isImpersonatedUserLoggedIn) || section.section !== 'ADMIN')
    .map(section => ({
      ...section,
      items: section.items.filter(item =>
        item.label.toLowerCase().includes(searchQuery.toLowerCase())
      )
    })).filter(section => section.items.length > 0);

  const menuItemsToShow = userData?.data?.type === 'SELLER' ? filteredMenuItemsSeller : filteredMenuItems;

  const filteredSavedBomData = useMemo(() =>
    Array.isArray(leftPanelData) && leftPanelData?.filter((item: any) => item?.title?.toLowerCase().includes(searchQuery.toLowerCase())),
    [leftPanelData, searchQuery]
  );
  const filteredViewPoHistoryData = useMemo(() =>
    Array.isArray(viewPoHistoryData) && viewPoHistoryData?.filter((item: any) => item?.buyer_internal_po?.toLowerCase().includes(searchQuery.toLowerCase()) || item?.buyer_po_number?.toLowerCase().includes(searchQuery.toLowerCase()) ),
    [viewPoHistoryData, searchQuery]
  );

  const toggleAlwaysOnTop = () => {
      const value = !isSticky;
      if(channelWindow?.sticky){
          window.electron.send({ channel: channelWindow.sticky, data: value });
      }
      sessionStorage.setItem(localStorageStickyItemKey, value.toString());
      setIsSticky(value);
  }

  const handleLogout = () => {
      setCloseWithoutAnimation(true);
      setOpenLeftPanel(false);
      initiateLogout(false, false, true);
  }

  const ignoreMouseEvents = () => {
      if(!showLeftPanel){
          setTimeout(() => {
              handleMouseLeave();
          }, 1000);
      }
  }

  const handleMouseEnter = () => {
      if(channelWindow?.ignoreMouseEvents && !(showLeftPanel)){
          window.electron.send({ channel: channelWindow.ignoreMouseEvents, data:true})
      }
  }

  const handleMouseLeave = () => {
      if(channelWindow?.ignoreMouseEvents && !(showLeftPanel)){
          window.electron.send({ channel: channelWindow.ignoreMouseEvents, data:false})
      }
  }

  const handleHomeKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if(e.key === 'Enter' || e.key === ' '){
      e.preventDefault();
      handleHomePageClick()
    }
  }

  const handleHomePageClick = () => {
    if(location.pathname === routes.savedBom){
      setDisplayLeftPanel(true);
      setSearchQuery('');
    }
    if(location.pathname === routes.viewPoHistory){
      setDisplayLeftPanel(true);
      setSearchQuery('');
    }
  }

  const handlePinKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if(e.key === 'Enter' || e.key === ' '){
      e.preventDefault();
      toggleAlwaysOnTop()
    }
  }
  const handleNavigationConfirmYes = (to: string) => {
    if(bomDataIdToRefresh){
      const socket = getSocketConnection();
      socket?.emit('getBomDataById', { bom_upload_id: bomDataIdToRefresh });
      setBomDataIdToRefresh(null);
    }
    setShowBackToBomUploadButton(false);
    navigatePage(location.pathname, {path:to})
    resetDialogStore();
    setIsCreatePoDirty(false)
  }

  const handleNavigationWithConfirmation = (shouldConfirm: boolean = false, to: string, message: string) => {
    if (shouldConfirm) {
      showCommonDialog(
        null, 
        message, 
        null, 
        resetDialogStore, 
        [
            {
                name: 'Yes', 
                action: () => handleNavigationConfirmYes(to)
            }, 
            {
                name: 'No', 
                action: resetDialogStore
            }
        ]
    );
      return;
    }
      // If no confirmation needed, just navigate
    navigatePage(location.pathname, {path:to})
  };

  const handleQuestionIconClick = () => {
    console.log("handleQuestionIconClick", isHoverVideoEnabled);
    toggleHoverVideo();
  };

  useEffect(() => {
    if((noInternetAccessibility || apiFailureDueToNoInternet || !onlineStatus)){ 
      setLeftPanelHeight(752)
    } else {
      setLeftPanelHeight(routerContentRef.current?.offsetHeight)
    }
  }, [location.pathname, routerContentRef.current?.offsetHeight, noInternetAccessibility, apiFailureDueToNoInternet, onlineStatus]);

  useEffect(() => {
    let isSticky = sessionStorage.getItem(localStorageStickyItemKey)==='true' ? true : false;
    if(channelWindow?.sticky){
      window.electron.send({ channel: channelWindow.sticky, data: isSticky });
    }
    setIsSticky(isSticky);
  }, []);


  return (
    <div className={clsx(styles.menuParentContainer)}
      onClick={ignoreMouseEvents}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {(location.pathname !== routes.loginPage && location.pathname !== routes.forgotPassword) && <div className={clsx(styles.slideInContainer, (showLeftPanel) ? styles.slideIn : styles.slideOut)}>
      <div className={clsx('wrapperOverlay', styles.wrapperOverlay )}></div>
    <div className={clsx(styles.menuContainer, showLeftPanel ? styles.openLeftPanel : styles.closeMenu, (location.pathname === routes.homePage) ? (userData?.data?.type === 'SELLER' || (userSubscription?.id) ? styles.sideMenuTallerSeller : styles.sideMenuTaller) : (userData?.data?.type === 'SELLER' || (userSubscription?.id)) ? styles.sideMenuShorterSeller : styles.sideMenuShorter)} style={{ height: leftPanelHeight }}>
      {isMenuOpen && <div className={styles.sideMenu}>

        {(updateBackdropOverlay) && 
          <div className='backdropOverlay' />
        }
        { location.pathname === routes.homePage ?
          <div className={styles.sideMenuRow1Tall} data-hover-video-id='main-menu'>
          <div className={styles.sideMenuHeader}>
              <div className={clsx(styles.sideMenuHeaderIcon,location.pathname !== routes.savedBom && styles.active)} onClick={handleHomePageClick} 
              onKeyDown={handleHomeKeyDown}
              tabIndex={1}>
                <span className={styles.iconDivImg1}><HomeIcon /></span>
                <span className={styles.iconDivImg2}><HomeActiveIcon /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon,styles.disabledBtn)} tabIndex={2}>
                <span className={styles.iconDivImg1}><NotificationIcon /></span>
                <span className={styles.iconDivImg2}><NotificationActiveIcon /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon,styles.disabledBtn)} tabIndex={3}>
                <span className={styles.iconDivImg1}><ChatIcon /></span>
                <span className={styles.iconDivImg2}><ChatActiveIcon /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon, isSticky && styles.active)} onClick={() => toggleAlwaysOnTop()} onKeyDown={handlePinKeyDown} tabIndex={4} 
                data-hover-video-id='pin'>
                <span className={styles.iconDivImg1}><PinIcon /></span>
                <span className={styles.iconDivImg2}><PinActiveIcon /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon, location.pathname === routes.videoLibrary && styles.active)} tabIndex={5} onClick={() => navigatePage(location.pathname, { path: routes.videoLibrary })}>
                <span className={styles.iconDivImg1}><VideoIcon /></span>
                <span className={styles.iconDivImg2}><VideoActiveIcon /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon, isHoverVideoEnabled && styles.active)} 
                   tabIndex={6} 
                   onClick={handleQuestionIconClick}>
                <span className={styles.iconDivImg1}><QuestionIcons /></span>
                <span className={styles.iconDivImg2}><QuestionActiveIcons /></span>
              </div>
          </div>
          <div className={styles.searchBoxDiv}>
            <div className={styles.searchBox}>
                <SearchIcon />
                <input 
                  placeholder={(location.pathname === routes.savedBom && !displayLeftPanel) ? "Search Saved BOMs" : "Search Menu"} 
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  tabIndex={7}
                />
            </div>
          </div>
        </div>
          
        :
        <div className={styles.sideMenuRow1Small} data-hover-video-id='main-menu'>
          <div className={styles.sideMenuHeader}>
              <div className={clsx(styles.sideMenuHeaderIcon,(location.pathname !== routes.savedBom  || displayLeftPanel) && styles.active )} onClick={handleHomePageClick} onKeyDown={handleHomeKeyDown} tabIndex={1} >
                <span className={styles.iconDivImg1}><HomeIcons /></span>
                <span className={clsx(styles.iconDivImg2,styles.homeActiveIcon)} ><HomeActiveIcons /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon,styles.disabledBtn)} tabIndex={2}>
                <span className={styles.iconDivImg1}><NotificationIcons /></span>
                <span className={styles.iconDivImg2}><NotificationActiveIcons /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon,styles.disabledBtn)} tabIndex={3}>
                <span className={styles.iconDivImg1}><ChatIcons /></span>
                <span className={styles.iconDivImg2}><ChatActiveIcons /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon, isSticky && styles.active)} onClick={() => toggleAlwaysOnTop()} onKeyDown={handlePinKeyDown} tabIndex={4} data-hover-video-id='pin'>
                <span className={styles.iconDivImg1}><PinIcons /></span>
                <span className={styles.iconDivImg2}><PinActiveIcons /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon, location.pathname === routes.videoLibrary && styles.active)} tabIndex={5} onClick={() => navigatePage(location.pathname, { path: routes.videoLibrary })}>
                <span className={styles.iconDivImg1}><VideoIcons /></span>
                <span className={styles.iconDivImg2}><VideoActiveIcons /></span>
              </div>
              <div className={clsx(styles.sideMenuHeaderIcon, isHoverVideoEnabled && styles.active)} 
                   tabIndex={6} 
                   onClick={handleQuestionIconClick}>
                <span className={styles.iconDivImg1}><QuestionIcons /></span>
                <span className={styles.iconDivImg2}><QuestionActiveIcons /></span>
              </div>
          </div>
          <div className={styles.searchBoxDiv}>
            <div className={styles.searchBox}>
                <SearchIcon />
                <input 
                  placeholder={(location.pathname === routes.savedBom && !displayLeftPanel) ? "Search Saved BOMs" : "Search Menu"} 
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  tabIndex={7}
                />
            </div>
          </div>
        </div>
        }
        { (userData?.data?.type === 'BUYER' && (!userSubscription || !userSubscription?.id) ) &&
          <div className={styles.sideMenuRow2}>
            <span className={styles.freeTrialText}>FREE   TRIAL EXPIRES IN 14 DAYS</span>
            <span className={styles.subscribeButton}>
              <button tabIndex={8} onClick={() => navigatePage(location.pathname, { path: routes.subscribe })}>
                SUBSCRIBE
              </button>
            </span>
            <span className={styles.learnMoreText}>LEARN MORE</span>
          </div>
        }
            {(isImpersonatedUserLoggedIn && location.pathname !== routes.successPage) &&
              <span className={styles.impersonatedUserMain}>
                <span className={styles.impersonatedUserLabel}>IMPERSONATED USER</span>
                <span className={styles.impersonatedUserName}>{userData?.data?.email_id} {(currentTandC !== acceptedTandC || currentTandC === null || acceptedTandC === null) && <span>(TnC not accepted)</span>}</span>
              </span>
            }
        <div className={clsx(styles.sideMenuRow3, isImpersonatedUserLoggedIn && styles.impersonatedUserScroll, userData?.data?.type === 'SELLER' && styles.sideMenuRow3Seller, (userData?.data?.type === 'BUYER' && (!userSubscription || !userSubscription?.id)) ?   '' : styles.subscribeScroll)}>
          {
            openLeftPanel && (
                 <div className={clsx(styles.sideMenu3Scroller, (location.pathname === routes.savedBom && !displayLeftPanel) && styles.savedBomScroller)}>
                  {
                    (location.pathname === routes.savedBom && !displayLeftPanel) ? (
                      <SavedBomLeftPanel savedBomData={filteredSavedBomData} />
                    ) : (location.pathname === routes.viewPoHistory && !displayLeftPanel) ? (
                      <ViewPoHistoryLeftPanel viewPoHistoryDataList={filteredViewPoHistoryData} />
                    ) : (
                      <>{menuItemsToShow.map((section, index) => (
                <div key={index} className={styles.sideMenuDiv}>
                  <span className={clsx(styles.lineBackground, styles.firstLine)}>
                    {section.section}
                  </span>
                  {section.items.map((item, itemIndex) => {
                    const isArrayRoute = Array.isArray(item.route);
                    let navigatePath;
                    if(isArrayRoute){
                      navigatePath = item.route[0];
                    }
                    else{
                      navigatePath = item.route;
                    }

                    const handleButtonClick = () => {
                      if (item.route) {
                        if (isSharedPricingHistory || isPriceSearchHistory) {
                          setIsSharedPricingHistory(false);
                          setIsPriceSearchHistory(false);
                          setLoadComponent(null);
                        }
                        if ((location.pathname === routes.createPoPage || location.pathname === routes.bomUploadReview)) {
                          const isDirty = isCreatePoDirty || !isCreatePOModule;
                          const message = isCreatePoDirty
                            ? navigationConfirmMessages.unsavedChanges
                            :
                            navigationConfirmMessages.confirmLeave;
                          if (item.route === routes.savedBom) {
                            setDisplayLeftPanel(false);
                            setSearchQuery('');
                          }
                          if (item.route === routes.viewPoHistory) {
                            setDisplayLeftPanel(false);
                            setSearchQuery('');
                          }

                          if (item.label === "Upload New BOM" && bomProgressSocketData) {
                            navigatePath = item.route[1];
                          }
                          handleNavigationWithConfirmation(isDirty, navigatePath.toString(), message)
                        } else {
                          if (item.route === routes.savedBom) {
                            setDisplayLeftPanel(false);
                            setSearchQuery('');
                          }
                          if (item.route === routes.viewPoHistory) {
                            setDisplayLeftPanel(false);
                            setSearchQuery('');
                          }
                          if (item.label === "Upload New BOM" && bomProgressSocketData) {
                            navigatePath = item.route[1];
                          }
                          item.route && navigatePage(location.pathname, { path: navigatePath })
                        }
                      }
                      if (item.label === "Shared Pricing") {
                        if (!isSharedPricingHistory) {
                          setLoadComponent(<SharedPricingHistoryWindow />);
                          setIsSharedPricingHistory(true);
                        } else {
                          setIsSharedPricingHistory(false);
                          setLoadComponent(null);
                        }

                      }

                      if (item.label === "Saved Searches") {
                        if (!isPriceSearchHistory) {
                          setLoadComponent(<SearchHistoryWindow />);
                          setIsPriceSearchHistory(true);
                        } else {
                          setIsPriceSearchHistory(false);
                          setLoadComponent(null);
                        }
                      }

                      if (item.label === "Chats") {
                        setLoadComponent(<MyChats />);
                      }

                      if (item.label === "Exit Impersonation") {
                        setTriggerExitImpersonation(true)
                      }
                    }

                    return(
                    <button
                      key={itemIndex}
                      disabled={item.label === "Impersonate User" && isImpersonatedUserLoggedIn || item.label === "Exit Impersonation" && !isImpersonatedUserLoggedIn}
                      data-hover-video-id={item?.videoHoverId?item.videoHoverId:""}
                      className={clsx(
                        styles.lineBackground,
                        ((isArrayRoute  &&  item.route.find(x=> x === location.pathname) || location.pathname === item.route) && !isSharedPricingHistory && !isPriceSearchHistory) && styles.lineBackgroundActive,
                        (isSharedPricingHistory && item.label === "Shared Pricing") && styles.lineBackgroundActive,
                        (isPriceSearchHistory && item.label === "Saved Searches") && styles.lineBackgroundActive,
                        (item.label === "Impersonate User" && isImpersonatedUserLoggedIn) && styles.impersonateUserDisabled,
                        (item.label === "Exit Impersonation" && !isImpersonatedUserLoggedIn) && styles.impersonateUserDisabled,
                        itemIndex === section.items.length - 1 && styles.lastLine
                      )}
                      onClick={handleButtonClick}
                      tabIndex={9}
                    >
                      {item.label}
                    </button>
                  )})}
                </div>
              ))}
                      </>
                    )
                  }
              

              </div>
            )
          }
          </div>
        <div className={styles.sideMenuRow4}>
            <button onClick={handleLogout} tabIndex={10}>Logout</button>
        </div>
        {<>
        {/* <div className={styles.sideMenuRow1Tall}>
          <div className={styles.sideMenuHeader}>
              <div className={clsx(styles.sideMenuHeaderIcon,location.pathname === routes.homePage && styles.active)} onClick={() => navigatePage(location.pathname, {path:routes.homePage})} >
                <span className={styles.iconDivImg1}><HomeIcon /></span>
                <span className={styles.iconDivImg2}><HomeActiveIcon /></span>
              </div>
              <div className={styles.sideMenuHeaderIcon}>
                <span className={styles.iconDivImg1}><NotificationIcon /></span>
                <span className={styles.iconDivImg2}><NotificationActiveIcon /></span>
              </div>
              <div className={styles.sideMenuHeaderIcon}>
                <span className={styles.iconDivImg1}><ChatIcon /></span>
                <span className={styles.iconDivImg2}><ChatActiveIcon /></span>
              </div>
              <div className={styles.sideMenuHeaderIcon} id='toggle-sticky-btn' >
                <span className={styles.iconDivImg1}><PinIcon /></span>
                <span className={styles.iconDivImg2}><PinActiveIcon /></span>
              </div>
              <div className={styles.sideMenuHeaderIcon}>
                <span className={styles.iconDivImg1}><VideoIcon /></span>
                <span className={styles.iconDivImg2}><VideoActiveIcon /></span>
              </div>
              <div className={styles.sideMenuHeaderIcon}>
                <span className={styles.iconDivImg1}><QuestionIcon /></span>
                <span className={styles.iconDivImg2}><QuestionActiveIcon /></span>
              </div>
          </div>
          <div className={styles.searchBoxDiv}>
            <div className={styles.searchBox}>
                <SearchIcon />
                <input 
                  placeholder="Search Menu" 
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
            </div>
          </div>
          <div className={styles.sideMenuBgEllips}></div>
          <div className={styles.sideMenuBgEllips1}></div>
        </div>  */}
        </>}
      </div>}
    </div>
    </div>}
    </div>
  );
}

export default LeftPanel

